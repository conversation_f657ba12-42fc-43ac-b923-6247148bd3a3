import { useState, useRef, useEffect } from 'react'
import { Pencil, Trash2, Terminal, HardDrive, Network, User, Users, FolderPlus, Download, MoreVertical } from 'lucide-react'
import { SSHServer, Command } from '../types/server'
import CommandModal from './CommandModal'
import EditServerModal from './EditServerModal'
import ConfirmModal from './ConfirmModal'
import ServerUserAccess from './ServerUserAccess'
import ServerGroupAssignment from './ServerGroupAssignment'
import ServerBackupModal from './ServerBackupModal'
import { api } from '../lib/api'
import { useAuth } from '../contexts/AuthContext'

interface ServerCardProps {
  server: SSHServer & { commands: Command[] }
  onServerUpdated: () => void
}

export function ServerCard({ server, onServerUpdated }: ServerCardProps) {
  const { user: currentUser } = useAuth()
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [isConfirmDeleteOpen, setIsConfirmDeleteOpen] = useState(false)
  const [isUserAccessModalOpen, setIsUserAccessModalOpen] = useState(false)
  const [isGroupAssignmentOpen, setIsGroupAssignmentOpen] = useState(false)
  const [isBackupModalOpen, setIsBackupModalOpen] = useState(false)
  const [isDropdownOpen, setIsDropdownOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  // Verificar se o usuário atual é o proprietário do servidor ou um administrador
  const isOwnerOrAdmin = server.userId === currentUser?.id || currentUser?.role === 'ADMIN'

  // Fechar dropdown quando clicar fora e calcular posição
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  // Toggle do dropdown
  const handleDropdownToggle = () => {
    setIsDropdownOpen(!isDropdownOpen)
  }

  async function handleDelete() {
    try {
      await api.delete(`/api/servers/${server.id}`)
      onServerUpdated()
    } catch (error) {
      console.error('Erro ao excluir servidor:', error)
      alert('Erro ao excluir servidor. Por favor, tente novamente.')
    }
  }

  return (
    <>
      <div className="bg-white rounded-xl shadow-lg border border-gray-100 hover:border-primary-100 transition-all duration-300 relative h-full flex flex-col">
        <div className="p-4 sm:p-6 space-y-3 sm:space-y-4 flex-1 flex flex-col">
          {/* Cabeçalho com nome e ações */}
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-2 sm:gap-3">
              <HardDrive className="h-5 w-5 sm:h-6 sm:w-6 text-primary-500" />
              <h2 className="text-sm sm:text-md font-semibold text-gray-800 tracking-tight">{server.name}</h2>
            </div>

            {/* Menu de ações */}
            <div className="relative" ref={dropdownRef}>
              <button
                onClick={handleDropdownToggle}
                className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-50 rounded-lg transition-colors"
                title="Ações do servidor"
              >
                <MoreVertical className="h-4 w-4" />
              </button>

              {/* Menu dropdown */}
              {isDropdownOpen && (
                <div className="absolute right-0 top-full mt-1 w-48 bg-white rounded-lg shadow-2xl border border-gray-200 py-1"
                style={{ zIndex: 9999 }}>
                  {/* Backup/Importação */}
                  <button
                    onClick={() => {
                      setIsBackupModalOpen(true)
                      setIsDropdownOpen(false)
                    }}
                    className="w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center gap-2"
                  >
                    <Download className="h-4 w-4 text-green-600" />
                    Backup/Importação
                  </button>

                  {/* Gerenciar grupos */}
                  <button
                    onClick={() => {
                      setIsGroupAssignmentOpen(true)
                      setIsDropdownOpen(false)
                    }}
                    className="w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center gap-2"
                  >
                    <FolderPlus className="h-4 w-4 text-blue-600" />
                    Gerenciar Grupos
                  </button>

                  {/* Gerenciar usuários - apenas para admin */}
                  {currentUser?.role === 'ADMIN' && (
                    <button
                      onClick={() => {
                        setIsUserAccessModalOpen(true)
                        setIsDropdownOpen(false)
                      }}
                      className="w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center gap-2"
                    >
                      <Users className="h-4 w-4 text-primary-600" />
                      Gerenciar Usuários
                    </button>
                  )}

                  {/* Separador */}
                  {isOwnerOrAdmin && (
                    <div className="border-t border-gray-100 my-1"></div>
                  )}

                  {/* Ações do proprietário/admin */}
                  {isOwnerOrAdmin && (
                    <>
                      <button
                        onClick={() => {
                          setIsEditModalOpen(true)
                          setIsDropdownOpen(false)
                        }}
                        className="w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center gap-2"
                      >
                        <Pencil className="h-4 w-4 text-primary-600" />
                        Editar Servidor
                      </button>
                      <button
                        onClick={() => {
                          setIsConfirmDeleteOpen(true)
                          setIsDropdownOpen(false)
                        }}
                        className="w-full px-3 py-2 text-left text-sm text-red-600 hover:bg-red-50 flex items-center gap-2"
                      >
                        <Trash2 className="h-4 w-4" />
                        Excluir Servidor
                      </button>
                    </>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Informações do servidor */}
          <div className="space-y-2 sm:space-y-3 pt-1 sm:pt-2 flex-1">
            <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2 text-xs sm:text-sm text-gray-600">
              <div className="flex items-center gap-2">
                <Network className="h-4 w-4 text-gray-400" />
                <span className="font-medium">Host:</span>
                <span className="font-mono bg-gray-50 px-2 py-0.5 rounded">{server.host}</span>
              </div>
              <div className="flex items-center gap-2 mt-1 sm:mt-0">
                <span className="font-medium sm:ml-2">Porta:</span>
                <span className="font-mono bg-gray-50 px-2 py-0.5 rounded">{server.port}</span>
              </div>
            </div>

            <div className="flex items-center gap-2 text-xs sm:text-sm text-gray-600">
              <User className="h-4 w-4 text-gray-400" />
              <span className="font-medium">Usuário:</span>
              <span className="font-mono bg-gray-50 px-2 py-0.5 rounded">{server.username}</span>
              <span className="font-medium ml-2">Tipo:</span>
              <span className="bg-primary-50 text-primary-700 px-2 py-0.5 rounded text-xs font-medium">
                SSH
              </span>
            </div>

            {/* Grupos do servidor - sempre reserva espaço para manter altura consistente */}
            <div className="mt-2 min-h-[28px] flex items-start">
              {server.groupMembers && server.groupMembers.length > 0 ? (
                <div className="flex flex-wrap gap-1 sm:gap-2 w-full">
                  {server.groupMembers.map((member) => (
                    <div
                      key={member.id}
                      className="flex items-center gap-1 px-2 py-1 rounded-full text-xs"
                      style={{
                        backgroundColor: `${member.group.color || '#3B82F6'}20`,
                        color: member.group.color || '#3B82F6'
                      }}
                    >
                      <div
                        className="w-2 h-2 rounded-full"
                        style={{ backgroundColor: member.group.color || '#3B82F6' }}
                      />
                      <span className="font-medium">{member.group.name}</span>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-xs text-gray-400 italic">
                  Nenhum grupo atribuído
                </div>
              )}
            </div>
          </div>

          {/* Botão de terminal */}
          <div className="pt-2 sm:pt-4 mt-auto">
            <button
              onClick={() => setIsModalOpen(true)}
              className={`w-full flex items-center justify-center gap-2 px-3 sm:px-4 py-2 sm:py-2.5 text-xs sm:text-sm font-medium rounded-lg transition-colors
                ${server.commands?.length > 0
                  ? 'bg-primary-600 text-white hover:bg-primary-700'
                  : 'bg-gray-200 text-gray-500 cursor-not-allowed'}`}
              title={server.commands?.length > 0 ? 'Abrir terminal' : 'Servidor sem comandos cadastrados'}
              disabled={!server.commands?.length}
            >
              <Terminal className="h-4 w-4" />
              {server.commands?.length > 0 ? 'Abrir Terminal' : 'Sem Comandos'}
            </button>
          </div>
        </div>
      </div>

      <CommandModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        server={server}
      />

      <EditServerModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        server={server}
        onServerUpdated={onServerUpdated}
      />

      <ConfirmModal
        isOpen={isConfirmDeleteOpen}
        onClose={() => setIsConfirmDeleteOpen(false)}
        onConfirm={handleDelete}
        title="Excluir Servidor"
        message="Tem certeza que deseja excluir este servidor? Esta ação não pode ser desfeita."
      />

      <ServerUserAccess
        isOpen={isUserAccessModalOpen}
        onClose={() => setIsUserAccessModalOpen(false)}
        serverId={server.id}
        serverName={server.name}
      />

      <ServerGroupAssignment
        isOpen={isGroupAssignmentOpen}
        onClose={() => setIsGroupAssignmentOpen(false)}
        server={server}
        onServerUpdated={onServerUpdated}
      />

      <ServerBackupModal
        isOpen={isBackupModalOpen}
        onClose={() => setIsBackupModalOpen(false)}
        server={server}
        onServerUpdated={onServerUpdated}
      />
    </>
  )
}